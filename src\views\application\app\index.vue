<template>
  <div class="full-height full-width">
    <search-list-content
      :labelWidth="isZh() ? '70px' : '100px'"
      class="full-height full-width"
      :table-loading="loading"
      :ref="searchTableRef"
      :tableData="tableData"
      :tablePage="tablePage"
      :columns="columns"
      @query="query"
      @reset="reset"
      @pagination="pagination"
      :inner="true"
    >
      <template #pageTitle>
        <page-header :title="$t('应用探索')">
          <!-- <template #titleWarning>{{ $t("pageTitle.app.desc") }}</template> -->
        </page-header>
      </template>
      <template slot="list">
        <el-tabs v-model="searchForm.appType" @tab-click="onTabClick">
          <el-tab-pane :label="'全部'" name="all">
            <span slot="label"
              ><i :class="'iconfont icon-quanbu'"></i> {{ "全部" }}</span
            >
          </el-tab-pane>
          <el-tab-pane
            v-for="item in difyAppTypes"
            :key="item.value"
            :label="item.label"
            :name="item.value"
            ><span slot="label"
              ><i :class="'iconfont ' + item.icon"></i> {{ item.label }}</span
            >
          </el-tab-pane>
        </el-tabs>
        <div class="app-list">
          <div
            class="app-box"
            v-for="data in tableData"
            :key="data.id"
            :name="data.id"
            @click="onClick(data)"
          >
            <el-card
              shadow="hover"
              class="app-item"
              :body-style="{ padding: '0px' }"
            >
              <div class="card-content">
                <div style="display: flex" class="card-top-content">
                  <div class="app-icon">
                    <div
                      class="emoji-icon"
                      :style="{ background: data.iconBackground }"
                      v-if="data.iconType == 'emoji'"
                    >
                      {{ data.icon }}
                    </div>
                    <!-- <img v-else :src="data.iconUrl" object-fit="contain" /> -->
                    <img v-else src="@/assets/404_images/test.png" object-fit="contain" />
                  </div>
                  <div class="app-right">
                    <div class="app-title" :title="data.name">
                      {{ data.name }}
                    </div>
                    <div class="app-desc">
                      <p class="app-intro" :title="data.description">
                          {{ data.description || $t("app.intro") }}
                      </p>
                    </div>
                  </div>
                </div>
                <div class="app-desc capbability-box mt10" :title="data.tags">
                  <span
                    class="capbability-tag"
                    v-for="i in data.tags"
                    :key="i"
                    >{{ i }}</span
                  >
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </template>
    </search-list-content>
  </div>
</template>
<script>
import Page from "@/components/searchListContent/mixins/page";
import { getAppList, getAppDifyUrl } from "@/api/ai/app";
import { trimObjectValue } from "@/utils/util";
export default {
  name: "appList",
  mixins: [Page],
  data() {
    return {
      searchForm: {
        appType: "all",
      },
      columns: [],
      difyAppTypes: this.getEnum(["difyAppTypes"]),
    };
  },
  methods: {
    request: getAppList,
    queryParams() {
      let params = {
        // ...this.extraParams,
        ...this.tablePage,
      };
      if (this.searchForm) {
        params = Object.assign(params, trimObjectValue(this.searchForm));
      }
      if (this.getExtraParams) {
        params = Object.assign(params, this.getExtraParams());
      }
      if (params.appType == "all") {
        params.appType = "";
      }
      return params;
    },
    onTabClick() {
      this.query();
    },
    loadPic(row) {
      try {
        return require(`./${row.iconUrl}.svg`);
      } catch (e) {
        return require(`./app-icon.png`);
      }
    },
    onClick(row) {
      this.$router.push({
        name: "newDifyPage",
        query: { appId: row.id },
      });
      return;
      getAppDifyUrl({ appId: row.id }).then((res) => {
        //更新应用会话ID
        let conversationIdInfo =
          JSON.parse(localStorage.getItem("conversationIdInfo")) || {};
        delete conversationIdInfo[row.id];
        localStorage.setItem(
          "conversationIdInfo",
          JSON.stringify(conversationIdInfo),
        );

        this.$router.push({
          name: "difyPage",
          query: { url: res.data, appId: row.id },
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.app-list {
  display: flex;
  flex-wrap: wrap;
  .app-box {
    flex: 0 0 25%;
    width: 25%;
    align-items: center;
    padding: 6px;
    word-break: break-all;
  }
  .app-item {
    height: 100%;
    cursor: pointer;
    .card-content {
      height: 100%;
      flex-direction: column;
      padding: 15px;
      .card-top-content {
        flex: 1;
        display: flex;
        align-items: flex-start;
        .app-icon {
          flex: 0 0 50px;
          width: 50px;
          height: 50px;
          margin-right: 10px;
          display: flex;
          align-items: flex-start;
          justify-content: flex-start;
          .emoji-icon {
            display: inline-block;
            width: 100%;
            height: 50px;
            border-radius: 10px;
            line-height: 50px;
            text-align: center;
            font-size: 26px;
          }
          img {
            width: 100%;
            height: 100%;
            border-radius: 4px;
            object-fit: cover;
            object-position: top left;
          }
        }
        .app-right {
          flex: 1;
          .card-right-btns {
            color: var(--color-primary);
          }
        }
        .app-title {
          font-weight: bold;
          font-size: 14px;
        }
        .app-desc {
          font-size: 12px;
          color: #666;
          p {
            margin: 5px 0px;
          }
          .app-intro {
            text-overflow: ellipsis;
            line-height: 20px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            line-clamp: 3;
            -webkit-line-clamp: 3;
            overflow: hidden;
          }
        }
      }
    }
    .el-tag + .el-tag {
      margin-left: 10px;
    }
  }
  .capbability-tag {
    display: inline-block;
    border: 1px solid var(--color-primary);
    padding: 0px 8px;
    border-radius: 50px;
    color: var(--color-primary);
    font-size: 12px;
    margin: 2px 4px;
  }
  .capbability-box {
    text-overflow: ellipsis;
    line-height: 20px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    line-clamp: 2;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }
}
</style>
